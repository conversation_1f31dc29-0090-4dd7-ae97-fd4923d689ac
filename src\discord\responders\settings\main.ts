import { createResponder, ResponderType } from "#base";
import { menus } from "#menus";

createResponder({
    customId: "settings/:menu",
    types: [ResponderType.Button], cache: "cached",
    async run(interaction, {menu}) {
        switch(menu) {
            case "main":
                interaction.reply(menus.settings.main());
                break;
            case "channels":
                interaction.reply({ content: "Menu de canais", ephemeral: true });
                break;
                case "parents":
                    interaction.reply({ content: "Menu de categorias", ephemeral: true });
                    break;
                    case "roles":
                        interaction.reply({ content: "Menu de cargos", ephemeral: true });
                break;
        }
    },
});