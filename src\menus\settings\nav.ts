import { ButtonBuilder, ButtonStyle } from "discord.js";
import { icon } from "functions/utils/emojis.js";

export const settingsNav = {
    main: new ButtonBuilder({
        customId: "settings/main",
        label: "Menu Principal",
        emoji: icon.house_01,
        style: ButtonStyle.Secondary,
    }),
    back: (menu: String) => new ButtonBuilder({
        customId: `settings/${menu}`,
        label: "Voltar",
        emoji: icon.arrow_left_md,
        style: ButtonStyle.Secondary,
    }),
};