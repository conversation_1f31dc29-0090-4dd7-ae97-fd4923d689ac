import { promises as fs } from "fs";
import { join } from "path";

interface SuggestionData {
    id: string;
    messageId: string;
    channelId: string;
    userId: string;
    content: string;
    author: string;
    createdAt: number;
}

interface VoteData {
    userId: string;
    type: "approve" | "disapprove";
}

interface PersistentData {
    suggestions: Record<string, SuggestionData>;
    votes: Record<string, VoteData[]>; // suggestionId -> votes
}

class VotesManager {
    private data: PersistentData = { suggestions: {}, votes: {} };
    private readonly filePath = join(process.cwd(), "data", "suggestions.json");

    constructor() {
        this.loadData();
    }

    private async ensureDataDir() {
        const dataDir = join(process.cwd(), "data");
        try {
            await fs.access(dataDir);
        } catch {
            await fs.mkdir(dataDir, { recursive: true });
        }
    }

    private async loadData() {
        try {
            await this.ensureDataDir();
            const fileContent = await fs.readFile(this.filePath, "utf-8");
            this.data = JSON.parse(fileContent);
        } catch (error) {
            // Se o arquivo não existir ou houver erro, usar dados vazios
            this.data = { suggestions: {}, votes: {} };
            await this.saveData();
        }
    }

    private async saveData() {
        try {
            await this.ensureDataDir();
            await fs.writeFile(this.filePath, JSON.stringify(this.data, null, 2));
        } catch (error) {
            console.error("Erro ao salvar dados das sugestões:", error);
        }
    }

    async createSuggestion(suggestionData: SuggestionData) {
        this.data.suggestions[suggestionData.id] = suggestionData;
        this.data.votes[suggestionData.id] = [];
        await this.saveData();
    }

    async getSuggestion(suggestionId: string): Promise<SuggestionData | null> {
        return this.data.suggestions[suggestionId] || null;
    }

    async getSuggestionByMessageId(messageId: string): Promise<{ suggestion: SuggestionData; id: string } | null> {
        for (const [id, suggestion] of Object.entries(this.data.suggestions)) {
            if (suggestion.messageId === messageId) {
                return { suggestion, id };
            }
        }
        return null;
    }

    async vote(suggestionId: string, userId: string, voteType: "approve" | "disapprove") {
        if (!this.data.votes[suggestionId]) {
            this.data.votes[suggestionId] = [];
        }

        const votes = this.data.votes[suggestionId];
        const existingVoteIndex = votes.findIndex(vote => vote.userId === userId);

        if (existingVoteIndex !== -1) {
            const existingVote = votes[existingVoteIndex];
            if (existingVote.type === voteType) {
                // Remove o voto se for o mesmo tipo
                votes.splice(existingVoteIndex, 1);
            } else {
                // Troca o tipo do voto
                existingVote.type = voteType;
            }
        } else {
            // Adiciona novo voto
            votes.push({ userId, type: voteType });
        }

        await this.saveData();
    }

    getVotes(suggestionId: string): VoteData[] {
        return this.data.votes[suggestionId] || [];
    }

    getVoteStats(suggestionId: string) {
        const votes = this.getVotes(suggestionId);
        const totalVotes = votes.length;
        const approveVotes = votes.filter(vote => vote.type === "approve").length;
        const disapproveVotes = votes.filter(vote => vote.type === "disapprove").length;

        const approvePercentage = totalVotes ? Math.round((approveVotes / totalVotes) * 100) : 0;
        const disapprovePercentage = totalVotes ? Math.round((disapproveVotes / totalVotes) * 100) : 0;

        return {
            total: totalVotes,
            approves: approveVotes,
            disapproves: disapproveVotes,
            approvePercentage,
            disapprovePercentage
        };
    }

    // Limpar sugestões antigas (mais de 7 dias)
    async cleanupOldSuggestions() {
        const sevenDaysAgo = Date.now() - (7 * 24 * 60 * 60 * 1000);
        let hasChanges = false;

        for (const [id, suggestion] of Object.entries(this.data.suggestions)) {
            if (suggestion.createdAt < sevenDaysAgo) {
                delete this.data.suggestions[id];
                delete this.data.votes[id];
                hasChanges = true;
            }
        }

        if (hasChanges) {
            await this.saveData();
        }
    }
}

export const votesManager = new VotesManager();

// Limpar dados antigos a cada hora
setInterval(() => {
    votesManager.cleanupOldSuggestions();
}, 60 * 60 * 1000);