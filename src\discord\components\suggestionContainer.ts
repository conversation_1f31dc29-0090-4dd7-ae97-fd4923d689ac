import { createContainer, createRow, createSeparator, createTextDisplay } from "@magicyan/discord";
import { ButtonBuilder, ButtonStyle } from "discord.js";
import { icon } from "functions/utils/emojis.js";

export function createSuggestionContainer(
    id: string,
    mensagem: string,
    author: string,
    approvePercentage = 0,
    disapprovePercentage = 0
) {
    return createContainer({
        accentColor: constants.colors.default,
        components: [
            createTextDisplay(`${icon.chat} Nova Sugestão`),
            createTextDisplay(`-# **Sugestão enviada por ${author}**`),
            createSeparator(true),
            createTextDisplay(`\`${mensagem}\``),
            createSeparator(true),
            createRow(
                new ButtonBuilder({
                    customId: `suggestion/${id}/approve`,
                    label: `Aprovar (${approvePercentage}%)`,
                    emoji: icon.check,
                    style: ButtonStyle.Secondary,
                }),
                new ButtonBuilder({
                    customId: `suggestion/${id}/disapprove`,
                    label: `Desaprovar (${disapprovePercentage}%)`,
                    emoji: icon.close_md,
                    style: ButtonStyle.Secondary,
                })
            )
        ]
    });
}