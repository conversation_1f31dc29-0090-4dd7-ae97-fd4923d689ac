import { Schema } from "mongoose";
import { t } from "../utils.js";

export const guildSchema = new Schema(
    {
        id: t.string,
        channels: {
            logs: t.channelInfo,
            transcripts: t.channelInfo,
            suggestions: t.channelInfo,
        },
        parents: {
            tickets: t.parentInfo,
        },
        tickets: {
            roles: [String],
        }
    },
    {
        statics: {
            async get(id: string) {
                return await this.findOne({ id }) ?? this.create({ id });
            }
        }
    }
);