{"name": "bot_val_versao_2", "type": "module", "main": "build/index.js", "scripts": {"check": "tsc --noEmit && echo ✔ Ok", "build": "tsup", "dev": "tsx --env-file .env ./src/index.ts", "dev:dev": "tsx --env-file .env.dev ./src/index.ts", "watch": "tsx --watch --env-file .env ./src/index.ts", "watch:dev": "tsx --watch --env-file .env.dev ./src/index.ts", "start": "node --env-file .env .", "start:dev": "node --env-file .env.dev ."}, "dependencies": {"@magicyan/discord": "1.5.1", "@reliverse/reglob": "1.0.0", "chalk": "5.5.0", "discord.js": "14.21.0", "rou3": "0.7.3", "zod": "4.0.17", "mongoose": "8.3.1"}, "devDependencies": {"@types/node": "22.16.4", "tsx": "4.19.3", "typescript": "5.7.2", "tsup": "^8.3.5"}, "imports": {"#env": ["./build/env.js"], "#base": ["./build/discord/base/index.js"], "#functions": ["./build/functions/index.js"], "#database": ["./build/database/index.js"], "#server": ["./build/server/index.js"], "#menus": ["./build/menus/index.js"], "#tools": ["./build/tools/index.js"], "#lib/*": ["./build/lib/*"]}, "baseVersion": "1.4.2"}