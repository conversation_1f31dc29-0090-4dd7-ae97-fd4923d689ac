{
	"compilerOptions": {
		// General options
		"esModuleInterop": true,
		"forceConsistentCasingInFileNames": true,
		"skipLibCheck": true,
		"resolveJsonModule": true,
		"strict": true,

		// Language and environment options
		"lib": ["ESNext"],
		"target": "ESNext",
		"module": "NodeNext",
		"moduleResolution": "NodeNext",
		
		// Linting options
		"noUnusedLocals": true,
		"noUnusedParameters": true,
		"noImplicitReturns": true,
		"noImplicitOverride": true,
		"noEmitOnError": true,

		// Output Options
		"outDir": "./build",
		"rootDir": "./src",

		// Path mapping Options
		"baseUrl": "./src",
		"paths": {
			"#env": ["./env.ts"],
			"#base": ["./discord/base/index.ts"],
			"#functions": ["./functions/index.ts"],
			"#database": ["./database/index.ts"],
			"#server": ["./server/index.ts"],
			"#menus": ["./menus/index.ts"],
			"#tools": ["./tools/index.ts"],
			"#lib/*": ["./lib/*"],
			"#emojis": ["../emojis.json"],
			"#types/*": ["./@types/*"],
		}
	},
	
	// Include and Exclude Options
	"include": ["src"],
	"exclude": ["node_modules"]
}