import { brBuilder, createContainer, createEmbed, createRow, createTextDisplay } from "@magicyan/discord";
import { ButtonBuilder, ButtonStyle } from "discord.js";
import { icon } from "functions/utils/emojis.js";

export function settingsMainMenu() {
    const container = createContainer({
        accentColor: constants.colors.default,
        components: [
            createTextDisplay("Configurações")
        ]
    });
    
    const embed = createEmbed({
        color: constants.colors.default,
        description: brBuilder(
            `${icon.settings} Configurações`,
            "- Definir canais",
            "- Definir categorias de ticket",
            "- Definir cargos de tickets",
        ),
    })

    const row = createRow(
        new ButtonBuilder({
            customId: "settings/channels",
            label: "Canais",
            emoji: icon.info,
            style: ButtonStyle.Secondary,
        }),
        new ButtonBuilder({
            customId: "settings/parents",
            label: "Categorias",
            emoji: icon.info,
            style: ButtonStyle.Secondary,
        }),
        new ButtonBuilder({
            customId: "settings/roles",
            label: "Cargos",
            emoji: icon.info,
            style: ButtonStyle.Secondary,
        }),
    )

    // return { ephemeral: true, embeds: [embed], components: [row] }
    return { ephemeral: true, flags: ["IsComponentsV2"], components: [container] };
}