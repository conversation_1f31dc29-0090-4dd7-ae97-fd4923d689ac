import { createResponder, ResponderType } from "#base";
import { createSuggestionContainer } from "discord/components/suggestionContainer.js";
import { votesManager } from "../data/votes.js";

createResponder({
    customId: "suggestion/:id/:action",
    types: [ResponderType.Button],
    cache: "cached",
    parse: ({ action }) => ({ action }),
    async run(interaction, { action }) {
        const userId = interaction.user.id;
        const messageId = interaction.message.id;

        // Buscar a sugestão pelo messageId
        const suggestionData = await votesManager.getSuggestionByMessageId(messageId);

        if (!suggestionData) {
            await interaction.reply({
                content: "Essa sugestão não existe mais.",
                ephemeral: true,
            });
            return;
        }

        const { suggestion, id: suggestionId } = suggestionData;

        // Processar o voto
        await votesManager.vote(suggestionId, userId, action as "approve" | "disapprove");

        // Obter estatísticas atualizadas
        const stats = votesManager.getVoteStats(suggestionId);

        // Criar container atualizado
        const updatedContainer = createSuggestionContainer(
            suggestionId,
            suggestion.content,
            suggestion.author,
            stats.approvePercentage,
            stats.disapprovePercentage
        );

        await interaction.update({
            components: [updatedContainer],
        });
    },
});