import { createEvent } from "#base";
import { createSuggestionContainer } from "discord/components/suggestionContainer.js";
import { icon } from "functions/utils/emojis.js";
import { votesManager } from "../data/votes.js";

createEvent({
    name: "suggestionMessage",
    event: "messageCreate",
    async run(message) {
        // Ignorar mensagens de bots
        if (message.author.bot) return;

        // Verificar se a mensagem foi enviada no canal de sugestões
        if (message.channelId !== constants.channels.suggestions) return;

        // Verificar se o canal é um canal de texto
        if (!message.channel || !('send' in message.channel)) return;

        // Ignorar mensagens vazias ou muito curtas
        if (!message.content || message.content.trim().length < 3) {
            await message.delete().catch(() => {});
            await message.author.send({
                content: "❌ Sua sugestão deve ter pelo menos 3 caracteres!"
            }).catch(() => {});
            return;
        }

        // Ignorar mensagens muito longas
        if (message.content.length > 1000) {
            await message.delete().catch(() => {});
            await message.author.send({
                content: "❌ Sua sugestão deve ter no máximo 1000 caracteres!"
            }).catch(() => {});
            return;
        }

        try {
            // Deletar a mensagem original
            await message.delete();

            // Criar ID único para a sugestão
            const suggestionId = `${message.guildId}-${Date.now()}`;

            // Criar o container da sugestão
            const container = createSuggestionContainer(
                suggestionId, 
                message.content, 
                message.author.username
            );

            // Enviar a sugestão no canal
            const suggestionMessage = await message.channel.send({
                flags: ["IsComponentsV2"],
                components: [container],
            });

            // Salvar a sugestão no sistema persistente
            await votesManager.createSuggestion({
                id: suggestionId,
                messageId: suggestionMessage.id,
                channelId: message.channelId,
                userId: message.author.id,
                content: message.content,
                author: message.author.username,
                createdAt: Date.now()
            });

            // Criar thread para discussão
            const thread = await suggestionMessage.startThread({
                name: message.content.slice(0, 100), // Nome do tópico baseado na mensagem
                autoArchiveDuration: 1440, // 24 horas
            });

            if (thread) {
                await thread.send({
                    content: `**Obrigado <@${message.author.id}> por sugerir algo!**\n\nUse este tópico para discutir a sugestão.`,
                });
            }

            // Enviar confirmação por DM
            await message.author.send({
                content: `${icon.bell_ring} Sua sugestão foi criada com sucesso!\n\n**Sugestão:** \`${message.content}\`\n\nVocê pode acompanhar as votações no canal de sugestões.`
            }).catch(() => {
                // Se não conseguir enviar DM, ignorar silenciosamente
            });

        } catch (error) {
            console.error("Erro ao processar sugestão:", error);
            
            // Tentar enviar mensagem de erro
            await message.author.send({
                content: "❌ Ocorreu um erro ao processar sua sugestão. Tente novamente mais tarde."
            }).catch(() => {});
        }
    },
});
